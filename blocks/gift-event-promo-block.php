<?php
// Get all field values
$intro_title = get_field('intro_title');
$intro_cta_text = get_field('intro_cta_text');
$intro_cta_link = get_field('intro_cta_link');

$description_intro = get_field('description_intro');
$description_bullets = get_field('description_bullets');
$description_bottom_text = get_field('description_bottom_text');

$video_url = get_field('video_url');
$video_overlay_text = get_field('video_overlay_text');
$video_title_quote = get_field('video_title_quote');

// Get upcoming event data
$upcoming_events = get_posts([
    'post_type' => 'event',
    'posts_per_page' => 1,
    'meta_key' => 'event_date',
    'orderby' => 'meta_value',
    'order' => 'ASC',
    'meta_query' => [
        [
            'key' => 'event_date',
            'value' => date('Y-m-d'),
            'compare' => '>=',
            'type' => 'DATE'
        ]
    ]
]);

$upcoming_event = $upcoming_events ? $upcoming_events[0] : null;

// Get event data from upcoming event or fallback to manual fields
if ($upcoming_event) {
    $event_title = get_the_title($upcoming_event);
    $event_date = get_field('event_date', $upcoming_event->ID);
    $event_location = get_field('event_location', $upcoming_event->ID);
    $event_limited_title = get_field('event_limited_title', $upcoming_event->ID);
    $event_limited_text = get_field('event_limited_text', $upcoming_event->ID);
    $event_link = get_field('event_link', $upcoming_event->ID);
    $event_short_description = get_field('event_short_description', $upcoming_event->ID);
} else {
    // Fallback to manual fields if no upcoming event
    $event_title = get_field('event_title');
    $event_date = get_field('event_date');
    $event_location = get_field('event_location');
    $event_limited_title = get_field('event_limited_title');
    $event_limited_text = get_field('event_limited_text');
    $event_link = get_field('event_link');
    $event_short_description = '';
}

// Keep manual fields for extra content
$event_extra_title = get_field('event_extra_title');
$event_extra_body = get_field('event_extra_body');
$event_spots = get_field('event_spots');
$event_status = get_field('event_status');
$event_cta_text = get_field('event_cta_text');
$event_cta_link = get_field('event_cta_link');
$button = get_field('intro_cta_link');
$logo = get_field('logo');
?>

<section class="giftEventPromoBlock dark" data-init data-show-cursor>
    <div class="contentWrapper">
        <div class="eventPromoGrid">
            <!-- Intro Card (links boven) -->
            <div class="promoCard introCard" data-animate="fade-in-up">
                <?php if ($intro_title): ?>
                    <h2 class="mediumTitle" data-lines data-words><?= nl2br(esc_html($intro_title)) ?></h2>
                <?php endif; ?>
                
                <? if ($button) : ?>
                    <?php render_button_from_array($button, "primary"); ?>
                <?php endif; ?>
            </div>

            <div class="promoCard descriptionCard" data-animate="fade-in-up">
                <?php if ($description_intro): ?>
                    <div class="cardIntro">
                        <?= $description_intro ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($description_bullets): ?>
                    <ul class="bulletList">
                        <?php foreach ($description_bullets as $bullet): ?>
                            <?php if (!empty($bullet['bullet_text'])): ?>
                                <li><?= esc_html($bullet['bullet_text']) ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
                
                <?php if ($description_bottom_text): ?>
                    <a class="bigButton" href="<?= esc_url(get_field('button')['url']) ?>" title="<?= esc_attr(get_field('button')['title']) ?>" target="_blank">
                        <span class="textTitle">
                            <?= esc_html($description_bottom_text) ?>
                        </span>
                    </a>
                <?php endif; ?>
            </div>

            <!-- Video Card (links onder) -->
            <div class="promoCard videoCard" data-animate="fade-in-up">
                <?php
                
                // Extract YouTube video ID from URL
function get_youtube_id($url) {
    if (empty($url)) return '';
    
    preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches);
    return isset($matches[1]) ? $matches[1] : '';
}

$youtube_id = get_youtube_id($video_url);
                if ($youtube_id):
                     ?>
                    <div class="videoWrapper">
                        <div class="videoContainer" data-video-id="<?= esc_attr($youtube_id) ?>">
                            <div class="videoThumbnail">
                                <img src="https://img.youtube.com/vi/<?= esc_attr($youtube_id) ?>/maxresdefault.jpg" alt="Video thumbnail">
                                <div class="playButton">
                                    <svg width="68" height="48" viewBox="0 0 68 48">
                                        <path d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z" fill="#f00"></path>
                                        <path d="M 45,24 27,14 27,34" fill="#fff"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($video_overlay_text): ?>
                            <div class="videoOverlay">
                                <?= esc_html($video_overlay_text) ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($video_title_quote): ?>
                            <div class="videoTitle">
                                <?= esc_html($video_title_quote) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="promoCard eventDetailsCard" data-animate="fade-in-up">
                <div class="eventHeader">
                     <?php if ($logo): ?>
                        <img class="logo lazy" data-src="<?= esc_url($logo['sizes']['medium']) ?>" alt="<?= esc_attr($logo['alt']) ?>" />
                    <?php endif; ?>
                    <div class="ticketIcon">
                        <svg viewBox="0 0 512 425.62">
                            <path d="M489.73,233.64c2.16-.14,4.36-.29,6.49-.04,7.04.84,12.37-1.54,15.78-7.88v-51.01c-2.07-5.93-3.55-12.15-6.31-17.74-6.92-14.04-18.26-23.22-33.39-27.16-4.87-1.27-7-3.73-8.51-8.3-10.87-32.7-21.96-65.32-33.06-97.93-5.27-15.48-18.2-24.96-33.13-23.43-6.16.63-12.24,3.01-18.14,5.21-67.69,25.21-135.32,50.56-202.98,75.84-40.23,15.03-80.47,30.03-120.74,44.95-2.92,1.08-6.11,1.52-9.21,2.05-23.81,4.07-40.09,19.6-45.33,43.31-.24,1.1-.79,2.14-1.19,3.2v51.01c3.41,6.33,8.72,8.31,15.78,7.93,15.65-.85,29.46,3.64,39.3,16.47,10.52,13.72,12.44,29.01,5.15,44.69-7.2,15.46-19.95,23.62-36.99,24.92-2.49.19-5.02.36-7.48.1-7-.74-12.43,1.42-15.75,7.9v50.01c.72,2.7,1.46,5.39,2.17,8.09,6.12,23.36,26.8,39.71,50.88,39.73,135.14.07,270.29.07,405.43,0,25.53-.01,46.15-16.95,51.99-42.16.44-1.9,1.01-3.78,1.53-5.67v-50.01c-3.42-6.74-9.13-8.32-16.24-7.93-17,.93-31.26-4.84-40.85-19.32-9.34-14.09-9.93-29.23-2.07-44.1,7.71-14.59,20.59-21.68,36.9-22.75ZM128.88,120.78c80.48-30.06,160.96-60.1,241.45-90.15,7.96-2.97,15.87-6.07,23.87-8.91,8.48-3.01,13.96-.52,16.86,7.96,10.56,30.88,20.9,61.84,31.48,92.71,1.57,4.57-.02,5.5-4.23,5.5-84.79-.07-169.59-.07-254.38-.08-21.32,0-42.65.02-63.97.03-1.81,0-3.62,0-5.42,0-.13-.46-.26-.93-.38-1.39,4.91-1.89,9.8-3.83,14.73-5.67ZM482.74,339.72c8.43,1.03,8.53,1.03,8.42,9.59-.13,9.65.39,19.41-.92,28.91-2.22,16.12-15.36,26.76-31.75,26.78-43.16.07-86.32.02-129.49.02-43.5,0-86.99,0-130.49,0-6.73,0-6.72-.03-6.78-6.9-.01-1.33.05-2.68-.09-4-.67-6.22-5.24-10.42-10.92-10.12-5.44.29-9.25,4.62-9.53,10.84q-.45,10.16-10.75,10.17c-35,0-69.99.01-104.99,0-21,0-34.51-13.36-34.63-34.26-.05-8.5.07-17-.02-25.5-.03-3.23,1.06-4.65,4.54-5,24.47-2.42,42.57-14.55,53.47-36.63,18.66-37.79-6.94-84.68-48.83-89.81q-9.15-1.12-9.18-10.37c0-7.67-.15-15.34.08-23,.55-18.37,14.49-31.93,32.87-31.96,36.83-.07,73.66-.04,110.49-.03,6.87,0,6.58.06,7.06,6.97.25,3.65.71,7.99,2.81,10.66,4.91,6.23,14.41,3.65,17.01-4.13.31-.93.48-1.94.56-2.92q.86-10.56,11.68-10.56c84.33,0,168.65,0,252.98,0,21.2,0,34.58,13.25,34.81,34.59.09,8.33-.11,16.67.04,25,.06,3.55-1.21,4.82-4.84,5.23-28.94,3.29-48.57,18.73-56.87,46.65-10.97,36.9,14.96,75.09,53.26,79.78Z"/>
                            <path d="M180.36,293.91c-5.35.42-8.99,4.5-9.11,10.51-.11,5.65-.02,11.3-.02,16.95,0,0,.01,0,.02,0,0,6.15-.15,12.3.04,18.44.18,5.8,4.28,9.98,9.55,10.21,5.15.22,10.43-3.78,10.6-9.24.39-12.61.45-25.26,0-37.86-.21-5.86-5.4-9.45-11.09-9.01Z"/>
                            <path d="M181.08,259.6c6.28.04,10.45-4.33,10.61-11.33.12-5.5.02-11,.03-16.5,0-5.5.08-11-.02-16.5-.13-7.17-4.1-11.58-10.3-11.66-6.17-.08-10.15,4.36-10.18,11.59-.06,11-.04,21.99.01,32.99.03,6.88,3.97,11.36,9.85,11.4Z"/>
                        </svg>
                    </div>
                </div>

                <!-- Event Info Grid -->
                <div class="eventInfoGrid">
                    <div class="infoSection">
                        <div class="textTitle smaller">NEXT EVENT</div>
                        <div class="text"><p><?= $event_date ? esc_html(date('d.m.y', strtotime($event_date))) : 'TBA' ?></p></div>
                    </div>

                    <div class="infoSection">
                        <div class="textTitle smaller">LOCATION</div>
                        <div class="text"><p><?= $event_location ? esc_html($event_location) : 'TBA' ?></p></div>
                    </div>

                    <?php if ($event_limited_title || $event_limited_text): ?>
                    <div class="infoSection">
                        <div class="textTitle smaller"><?= $event_limited_title ? esc_html(strtoupper($event_limited_title)) : 'LIMITED' ?></div>
                        <div class="text"><p><?= $event_limited_text ? esc_html($event_limited_text) : 'Limited Spots' ?></p></div>
                    </div>
                    <?php endif; ?>

                    <?php if ($event_status): ?>
                    <div class="infoSection">
                        <div class="textTitle smaller">PREVIOUS EVENTS</div>
                        <div class="text"><p><?= esc_html($event_status) ?></p></div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Event Link Button -->
                <?php if ($event_link): ?>
                    <a href="<?= esc_url($event_link) ?>" target="_blank" class="bigButton">
                        <span class="textTitle">GET EVENT TAILS</span>
                    </a>
                <?php endif; ?>

                <!-- Extra Content (if any) -->
                <?php if ($event_extra_title || $event_extra_body): ?>
                    <div class="extraContent">
                        <?php if ($event_extra_title): ?>
                            <h4 class="extraTitle"><?= esc_html($event_extra_title) ?></h4>
                        <?php endif; ?>

                        <?php if ($event_extra_body): ?>
                            <div class="extraBody">
                                <?= wpautop($event_extra_body) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
            
        </div>
    </div>
</section>
