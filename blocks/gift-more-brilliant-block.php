<?php
$title = get_field('title');
$subtitle = get_field('subtitle');
$coming_soon_text = get_field('coming_soon_text');
$course_promise_title = get_field('course_promise_title');
$course_promise_items = get_field('course_promise_items');
$pre_launch_title = get_field('pre_launch_title');
$pre_launch_items = get_field('pre_launch_items');
$full_course_value = get_field('full_course_value');
$early_bird_price = get_field('early_bird_price');
$testimonial_quote = get_field('testimonial_quote');
$testimonial_name = get_field('testimonial_name');
$testimonial_role = get_field('testimonial_role');
?>
<section class="giftMoreBrilliantBlock dark" data-init data-show-cursor>
  <div class="contentWrapper smaller">
    <div class="headerSection" data-parallax data-parallax-speed="1">
      <?php if ($title): ?>
        <h2 class="hugeTitle bigger"><?= wp_kses_post($title) ?></h2>
      <?php endif; ?>
      
      <?php if ($subtitle): ?>
        <div class="subtitle"><?= esc_html($subtitle) ?></div>
      <?php endif; ?>
      
      <?php if ($coming_soon_text): ?>
        <div class="comingSoon"><?= esc_html($coming_soon_text) ?></div>
      <?php endif; ?>
    </div>
    
    <div class="cardsSection">
      <!-- Course Promise Card -->
      <div class="promiseCard">
        <?php if ($course_promise_title): ?>
          <h3 class="normalTitle"><?= esc_html($course_promise_title) ?></h3>
        <?php endif; ?>
        
        <?php if ($course_promise_items): ?>
          <ul class="cardList">
            <?php foreach ($course_promise_items as $item): ?>
              <?php if (!empty($item['item_text'])): ?>
                <li><?= esc_html($item['item_text']) ?></li>
              <?php endif; ?>
            <?php endforeach; ?>
          </ul>
        <?php endif; ?>
      </div>
      
      <!-- Pre-Launch Benefits Card -->
      <div class="benefitsCard">
        <?php if ($pre_launch_title): ?>
          <h3 class="normalTitle"><?= esc_html($pre_launch_title) ?></h3>
        <?php endif; ?>
        
        <?php if ($pre_launch_items): ?>
          <ul class="cardList">
            <?php foreach ($pre_launch_items as $item): ?>
              <?php if (!empty($item['item_text'])): ?>
                <li><?= esc_html($item['item_text']) ?></li>
              <?php endif; ?>
            <?php endforeach; ?>
          </ul>
        <?php endif; ?>
      </div>
    </div>
    
    <div class="pricingSection">
      <?php if ($full_course_value || $early_bird_price): ?>
        <div class="pricingText">
          <?php if ($full_course_value): ?>
            <span class="fullPrice">FULL COURSE VALUE: <?= esc_html($full_course_value) ?></span>
          <?php endif; ?>
          <?php if ($early_bird_price): ?>
            <span class="separator"> | </span>
            <span class="earlyPrice">EARLY BIRD PRICE: <?= esc_html($early_bird_price) ?></span>
          <?php endif; ?>
        </div>
      <?php endif; ?>
    </div>
    
    <?php if ($testimonial_quote): ?>
      <div class="testimonialSection">
        <blockquote class="testimonialQuote">
          "<?= esc_html($testimonial_quote) ?>"
        </blockquote>
        
        <?php if ($testimonial_name || $testimonial_role): ?>
          <div class="testimonialMeta">
            <?php if ($testimonial_name): ?>
              <span class="testimonialName"><?= esc_html($testimonial_name) ?></span>
            <?php endif; ?>
            <?php if ($testimonial_role): ?>
              <span class="testimonialRole">, <?= esc_html($testimonial_role) ?></span>
            <?php endif; ?>
          </div>
        <?php endif; ?>
      </div>
    <?php endif; ?>
  </div>
</section>
