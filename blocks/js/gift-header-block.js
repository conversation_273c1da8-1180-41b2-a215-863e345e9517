$(document).ready(function() {
    $(document).on("initPage", function() {
        if ($(".giftHeaderBlock").length > 0 && $(window).width() > 580) {
            initGiftHeaderBlock();
        }
    });
});

function initGiftHeaderBlock() { 
    $(".giftHeaderBlock").each(function(i, el){
        var $image = $(el).find(".scaleWrapper");
        gsap.to($image, .01, {
            scale: 1.5,
            y: 50 + "%",
            scrollTrigger: {
                trigger: el,
                start: "top top",
                end: "bottom top",
                scrub: true,
            }
        });
    });
}