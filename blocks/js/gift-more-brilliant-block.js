// Gift More Brilliant Block JavaScript
$(document).ready(function() {
    $(document).on("initPage", function() {
        if ($(".giftMoreBrilliantBlock").length > 0) {
            initGiftMoreBrilliantBlock();
        }
    });
});

function initGiftMoreBrilliantBlock() {
    $(".giftMoreBrilliantBlock").each(function(i, el) {
        var $block = $(el);
        
        // Initialize scroll animations
        initScrollAnimations($block);
    });
}

function initScrollAnimations($block) {
    const $animatedElements = $block.find('[data-animate]');
    
    if ($animatedElements.length === 0) return;

    // Use Intersection Observer for better performance
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                const $element = $(entry.target);
                const delay = $element.index() * 100; // Stagger animation
                
                setTimeout(() => {
                    $element.addClass('animated');
                }, delay);
                
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    $animatedElements.each(function() {
        observer.observe(this);
    });
}
