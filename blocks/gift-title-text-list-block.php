<?php
$main_title = get_field('main_title');
$main_text = get_field('main_text');
$list = get_field('list'); // repeater: title, text
$button = get_field('button');
$button_subtitle = get_field('button_subtitle');

// Split list items into two halves for innerCols
$list_count = $list ? count($list) : 0;
$half_count = ceil($list_count / 2);
$left_inner = $list ? array_slice($list, 0, $half_count) : [];
$right_inner = $list ? array_slice($list, $half_count) : [];
?>
<section class="giftTitleTextList <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <div class="cols">
      <!-- Linkerkolom -->
      <div class="col leftCol">
        <div class="mainContent">
          <?php if ($main_title): ?>
            <h2 class="bigTitle" data-lines data-words><?= esc_html($main_title) ?></h2>
          <?php endif; ?>

          <?php if ($main_text): ?>
            <div class="mainText">
              <?= wpautop(wp_kses_post($main_text)); ?>
            </div>
          <?php endif; ?>
        </div>
      </div>

      <!-- Rechterkolom -->
      <div class="col rightCol">
        <?php if ($list): ?>
          <div class="listItems">
            <div class="innerCols">
              <div class="innerCol">
                <?php foreach ($left_inner as $item): ?>
                  <div class="listItem">
                    <?php if (!empty($item['title'])): ?>
                      <h3 class="textTitle smaller"><?= esc_html($item['title']) ?></h3>
                    <?php endif; ?>
                    <?php if (!empty($item['text'])): ?>
                      <div class="itemText"><?= wpautop(wp_kses_post($item['text'])); ?></div>
                    <?php endif; ?>
                  </div>
                <?php endforeach; ?>
              </div>

              <div class="innerCol">
                <?php foreach ($right_inner as $item): ?>
                  <div class="listItem">
                    <?php if (!empty($item['title'])): ?>
                      <h3 class="textTitle smaller"><?= esc_html($item['title']) ?></h3>
                    <?php endif; ?>
                    <?php if (!empty($item['text'])): ?>
                      <div class="itemText"><?= wpautop(wp_kses_post($item['text'])); ?></div>
                    <?php endif; ?>
                  </div>
                <?php endforeach; ?>
              </div>
            </div>
            <?php if ($button): ?>
            <div class="buttonWrapper">
              <?php render_button_from_array($button, "primary"); ?>
              <?php if ($button_subtitle): ?>
                <div class="textTitle smaller"><?= esc_html($button_subtitle) ?></div>
              <?php endif; ?>
            </div>
          <?php endif; ?>
          </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
</section>
