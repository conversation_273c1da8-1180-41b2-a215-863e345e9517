// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftTimeline {
  &:has(.normalTitle) {
    .timeline {
      margin-top: @vw60;
    }
  }
  .normalTitle.centered {
    text-align: center;
  }
  .timeline {
    position: relative;
    background-image: linear-gradient(to bottom, fade(@primaryColor,40%), fade(@primaryColor,40%));
    background-repeat: no-repeat;
    background-size: 1px 100%;
    background-position: 50% 0;
    &:before {
      content: '';
      position: absolute;
      left: 50%;
      top: 0;
      .transform(translate(-50%, 0));
      width: 0; height: 0;
      border-left: @vw25 solid transparent;
      border-right: @vw25 solid transparent;
      border-top: @vw44 solid @primaryColor;
    }
    // bottom arrow
    &:after {
      content: '';
      position: absolute;
      left: 50%;
      bottom: @vw10;
      .transform(translate(-50%, 50%));
      width: 0; height: 0;
      border-left: @vw12 solid transparent;
      border-right: @vw12 solid transparent;
      border-top: @vw18 solid @primaryColor;
    }
  }

  .item {
    position: relative;
    display: grid;
    grid-template-columns: 1fr @vw80 1fr;
    align-items: start;
    gap: @vw30;
    padding: @vw40 0;
    opacity: 0;
    .transform(translateY(@vw16));
    &:first-child {
      padding-top: @vw100;
      &:before, &:after {
        top: @vw100 + @vw50;
      }
    }
    &.inview {
      opacity: 1;
      .transform(translateY(0));
      transition: opacity .45s, transform .45s;
      -webkit-transition: opacity .45s, transform .45s;
    }
    // horizontal connector from center to content
    &:before {
      content: '';
      position: absolute;
      top: @vw80;
      height: 1px;
      width: @vw80;
      background: fade(@primaryColor, 40%);
      left: calc(50% ~"-" @vw80);
      transform: translateY(-50%);
    }

    // dot on the line
    &:after {
      content: '';
      width: @vw14;
      height: @vw14;
      border-radius: 50%;
      background: @primaryColor;
      position: absolute;
      left: 50%;
      transform: translate(-50%, -50%);
      top: @vw80;
    }

    .content {
      padding-right: @vw60;
    }
    .media {
      grid-column: 3 / 4;
      .innerImage {
        position: relative;
        width: 100%;
        .paddingRatio(4,3);
        height: 0;
      }
      img, video {
        .rounded(@vw14);
        position: absolute;
        top: 50%; left: 50%;
        .transform(translate(-50%, -50%));
        width: 100%; height: 100%;
        object-fit: cover; object-position: center;
      }
    }

    &.side-right {
      &:before { left: 50%; }
      .content { grid-column: 3 / 4; padding-right: 0; padding-left: @vw60; }
      .media { grid-column: 1 / 2; }
    }

    .date { color: @primaryColor; margin-bottom: @vw10; }
    .title { margin-bottom: @vw20; }
  }

  @media all and (max-width: 1080px) {
    .timeline {
      background-position: 50% 0;
      &:before { .transform(translate(-50%, -0%)); border-left: @vw16-1080 solid transparent; border-right: @vw16-1080 solid transparent;  }
      &:after { .transform(translate(-50%, 0%)); border-left: @vw10-1080 solid transparent; border-right: @vw10-1080 solid transparent; border-top: @vw16-1080 solid @primaryColor; }
    }
    .item {
      grid-template-columns: 1fr @vw50-1080 1fr;
      gap: @vw20-1080;
      padding: @vw30-1080 0;
      &:after { width: @vw14-1080; height: @vw14-1080; }
      &:before { width: @vw50-1080; left: calc(50% ~"-" @vw50-1080); }
      .content { padding-right: @vw40-1080; }
      &.side-right { .content { padding-left: @vw40-1080; } }
    }
  }

  @media all and (max-width: 580px) {
    .timeline {
      background-position: @vw16-580 0;
      &:after {
        left: 3%;
        bottom: 0;
      }
    }
    .item {
      display: block;
      padding-left: @vw16-580 + @vw16-580; // leave room for line
      &:after { left: @vw16-580; top: @vw16-580; transform: translate(-50%, 0); }
      &:before { display: none; }
      .content { padding: 0; }
      .media { margin-top: @vw30-580; }
    }
  }
}

