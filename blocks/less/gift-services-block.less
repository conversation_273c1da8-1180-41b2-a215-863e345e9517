// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftServicesBlock {
    .cols {
        display: flex;
        flex-wrap: wrap;
    }
    .pin-spacer {
    }
    .sectionHeader {
        position: absolute;
        top: 0;
        left: 0;
        padding-right: @vw100 + @vw30;
    }
    .col {
        position: relative;
        display: inline-block;
        width: 50%;
        vertical-align: top;
    }
    .button {
        margin-top: @vw30;
    }
    .service {
        display: flex;
        gap: @vw16;
        flex-direction: row;
        opacity: 0;
        .transform(translateY(@vw16));
        &.inview {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            -webkit-transition: opacity .45s, transform .45s;
        }
        &:not(:last-child) {
            margin-bottom: @vw50;
        }
        strong {
            font-size: @vw12;
            color: rgba(@hardBlack, .3);
            text-transform: uppercase;
            letter-spacing: .14em;
            font-weight: 300;
        }
        .innerCol {
            width: 50%;
            .rounded(@vw20);
            &:not(.imageCol) {
                background: @hardWhite;
                padding: @vw40;
            }
            &.imageCol {
                overflow: hidden;
                position: relative;
                img {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                }
            }
        }
    }
}

@media all and (max-width: 1080px) {
    .giftServicesBlock {
        .sectionHeader {
            padding-right: @vw40-1080;
        }
        .button {
            margin-top: @vw30-1080;
        }
        .col {
            &:first-child {
                width: 40%;
            }
            &:last-child {
                width: 60%;
            }
        }
        .service {
            gap: @vw16-1080;
            .transform(translateY(@vw16-1080));
            &:not(:last-child) {
                margin-bottom: @vw50-1080;
            }
            strong {
                font-size: @vw12-1080;
            }
            .innerCol {
                .rounded(@vw20-1080);
                &:not(.imageCol) {
                    padding: @vw40-1080;
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .giftServicesBlock {
        .cols {
            flex-direction: column;
        }
        .sectionHeader {
            position: relative;
            padding-right: 0;
            margin-bottom: @vw40-580;
        }
        .col {
            width: 100% !important;
        }
        .button {
            margin-top: @vw30-580;
        }
        .service {
            flex-direction: column;
            gap: @vw16-580;
            .transform(translateY(@vw16-580));
            &:not(:last-child) {
                margin-bottom: @vw50-580;
            }
            strong {
                font-size: @vw12-580;
            }
            .innerCol {
                width: 100%;
                .rounded(@vw20-580);
                &:not(.imageCol) {
                    padding: @vw40-580;
                }
                &.imageCol {
                    display: none;
                }
            }
        }
    }
}