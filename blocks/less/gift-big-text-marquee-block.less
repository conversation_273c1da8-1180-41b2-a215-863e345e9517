// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftBigTextMarquee {
    padding: 0 !important;
    .marqueeWrapper {
        position: relative;
    }
    .marquee {
        margin-top: @vw80;
        white-space: nowrap;
        .itemsContainer {
            display: inline-block;
            overflow: hidden;
            position: relative;
            vertical-align: middle;
            .item {
                display: inline-block;
                vertical-align: middle;
                margin: 0 @vw50;
            }
        }
    }
}

@media all and (max-width: 1080px) {
    .giftBigTextMarquee {
        .marquee {
            margin-top: @vw80-1080;
            .itemsContainer {
                .item {
                    margin: 0 @vw50-1080;
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .giftBigTextMarquee {
        .marquee {
            margin-top: @vw80-580;
            .itemsContainer {
                .item {
                    margin: 0 @vw30-580;
                }
            }
        }
    }
}
