// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftLatestNews {
    .col {
        width: 50%;
        display: inline-block;
        vertical-align: top;
    }
    .sliderWrapper {
        width: 100%;
        position: relative;
    }
    .button {
        margin-top: @vw40;
    }
    .slider {
        margin: @vw70 0;
        .slide {
            display: inline-block;
            width: (@vw186 * 3) + (@vw16 * 2);
            .rounded(@vw14);
            overflow: hidden;
            margin: 0 @vw8;
            color: @almostWhite;
            text-decoration: none;
            cursor: pointer;
            &:hover {
                .imageWrapper {
                    .innerImage {
                        img {
                            .transform(translate(-50%, -50%) scale(1.05));
                        }
                    }
                }
                .info {
                    .transform(translateY(0));
                }
            }
            * {
                cursor: pointer;
            }
            .imageWrapper {
                height: auto;
                width: 100%;
                .transform(translate3d(0,0,0));
                .innerImage {
                    overflow: hidden;
                    position: relative;
                    .paddingRatio(590,687);
                    height: 0;
                    img {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        .transform(translate(-50%, -50%));
                        .transitionMore(transform, .3s);
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        object-position: center;
                    }
                }
            }
            .info {
                color: @hardWhite;
                background: @primaryColor;
                padding: @vw35;
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                .transform(translateY(100%));
                .transitionMore(transform, .3s);
                .textTitle {
                    color: rgba(@hardWhite, .5);
                    margin-bottom: @vw10;
                }
            }
        }
    }
    .sliderIndicator {
        height: 2px;
        width: 50%;
        display: inline-block;
        margin-right: @vw40;
        background: rgba(@primaryColor, .2);
        position: relative;
        overflow: hidden;
        .innerBar {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 0%;
            background: @primaryColor;
        }
    }
}

@media all and (max-width: 1080px) {
    .giftLatestNews {
        .col {
            width: 100%;
            &:not(:last-child) {
                margin-bottom: @vw40-1080;
            }
        }
        .button {
            margin-top: @vw40-1080;
        }
        .slider {
            margin: @vw70-1080 0;
            .slide {
                width: (@vw186-1080 * 2) + (@vw16-1080);
                .rounded(@vw14-1080);
                margin: 0 @vw8-1080;
                .info {
                    padding: @vw35-1080;
                    .textTitle {
                        margin-bottom: @vw10-1080;
                    }
                }
            }
        }
        .sliderIndicator {
            margin-right: @vw40-1080;
        }
    }
}

@media all and (max-width: 580px) {
    .giftLatestNews {
        .col {
            width: 100%;
            &:not(:last-child) {
                margin-bottom: @vw40-580;
            }
        }
        .button {
            margin-top: @vw40-580;
        }
        .slider {
            margin: @vw70-580 0;
            .slide {
                width: 90%;
                .rounded(@vw14-580);
                margin: 0 @vw8-580;
                .info {
                    padding: @vw35-580;
                    .textTitle {
                        margin-bottom: @vw10-580;
                    }
                }
            }
        }
        .sliderIndicator {
            margin-right: @vw40-580;
        }
    }
}