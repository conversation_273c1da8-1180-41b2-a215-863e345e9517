// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftPartnersMarqueeBlock {
    .marqueeWrapper {
        position: relative;
    }
    .textTitle {
        text-align: center;
    }
    .marquee {
        margin-top: @vw80;
        white-space: nowrap;
        .itemsContainer {
            display: inline-block;
            overflow: hidden;
            position: relative;
            vertical-align: middle;
            .item {
                display: inline-block;
                vertical-align: middle;
                margin: 0 @vw50;
                a {
                    .transitionMore(opacity, .3s);
                    cursor: pointer;
                    &:hover {
                        opacity: .5;
                    }
                    * {
                        cursor: pointer;
                    }
                }
                img {
                    display: block;
                    width: auto;
                    height: @vw40;
                    object-fit: contain;
                }
            }
        }
    }
}

@media all and (max-width: 1080px) {
    .giftPartnersMarqueeBlock {
        .marquee {
            margin-top: @vw80-1080;
            .itemsContainer {
                .item {
                    margin: 0 @vw50-1080;
                    img {
                        height: @vw40-1080;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .giftPartnersMarqueeBlock {
        .marquee {
            margin-top: @vw80-580;
            .itemsContainer {
                .item {
                    margin: 0 @vw30-580;
                    img {
                        height: @vw50-580;
                    }
                }
            }
        }
    }
}
