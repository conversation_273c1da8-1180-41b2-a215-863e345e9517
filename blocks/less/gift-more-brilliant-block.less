// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftMoreBrilliantBlock {
    text-align: center;
    &.inview {
        .cardsSection {
            .promiseCard,
            .benefitsCard {
                opacity: 1;
                .transform(translateY(0));
                transition: opacity .45s, transform .45s;
                -webkit-transition: opacity .45s, transform .45s;
            }
        }
    }
    .headerSection {
        margin-bottom: @vw80;
        .hugeTitle {
            margin-bottom: @vw30;
        }
        
        .subtitle {
            margin-bottom: @vw20;
        }
    }
    
    .cardsSection {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: @vw16;
        margin-bottom: @vw80;
        .promiseCard,
        .benefitsCard {
            opacity: 0;
            .transform(translateY(@vw30));
            background: @almostWhite;
            color: @secondaryColor;
            padding: @vw40;
            .rounded(@vw16);
            text-align: left;
            
            .normalTitle {
                margin-bottom: @vw25;
            }
            
            .cardList {
                list-style: none;
                padding: 0;
                margin: 0;
                
                li {
                    position: relative;
                    padding-left: @vw20;
                    margin-bottom: @vw15;
                    font-size: @vw16;
                    line-height: 1.6;
                    color: #555;
                    
                    &:before {
                        content: "•";
                        color: #C7A56F;
                        font-weight: bold;
                        position: absolute;
                        left: 0;
                        top: 0;
                    }
                    
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }

    .hugeTitle {
        p {
            line-height: 1;
        }
    }
    
    .pricingSection {
        margin-bottom: @vw60;
        
        .pricingText {
            font-size: @vw18;
            color: #C7A56F;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;

            .fullPrice {
                color: #C7A56F;
            }

            .separator {
                color: #666;
                margin: 0 @vw15;
            }

            .earlyPrice {
                color: @primaryColor;
            }
        }
    }
    
    .testimonialSection {
       line-height: 1.5;
        .testimonialQuote {
            color: @almostWhite;
        }
        
        .testimonialMeta {
            color: @almostWhite;
            
            .testimonialName {
                color: @primaryColor;
            }
            
            .testimonialRole {
                font-weight: 300;
            }
            
        }
    }
}

@media all and (max-width: 1080px) {
    .giftMoreBrilliantBlock {
        .headerSection {
            margin-bottom: @vw80-1080;
            .hugeTitle {
                margin-bottom: @vw30-1080;
            }
            .subtitle {
                margin-bottom: @vw20-1080;
            }
        }

        .cardsSection {
            gap: @vw16-1080;
            margin-bottom: @vw80-1080;
            .promiseCard,
            .benefitsCard {
                .transform(translateY(@vw30-1080));
                padding: @vw40-1080;
                .rounded(@vw16-1080);
                .normalTitle {
                    margin-bottom: @vw25-1080;
                }
                .cardList {
                    li {
                        padding-left: @vw20-1080;
                        margin-bottom: @vw15-1080;
                        font-size: @vw16-1080;
                    }
                }
            }
        }

        .pricingSection {
            margin-bottom: @vw60-1080;
            .pricingText {
                font-size: @vw18-1080;
                .separator {
                    margin: 0 @vw15-1080;
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .giftMoreBrilliantBlock {
        .headerSection {
            margin-bottom: @vw80-580;
            .hugeTitle {
                margin-bottom: @vw30-580;
            }
            .subtitle {
                margin-bottom: @vw20-580;
            }
        }

        .cardsSection {
            grid-template-columns: 1fr;
            gap: @vw16-580;
            margin-bottom: @vw80-580;
            .promiseCard,
            .benefitsCard {
                .transform(translateY(@vw30-580));
                padding: @vw30-580;
                .rounded(@vw16-580);
                .normalTitle {
                    margin-bottom: @vw25-580;
                }
                .cardList {
                    li {
                        padding-left: @vw20-580;
                        margin-bottom: @vw15-580;
                        font-size: @vw14-580;
                    }
                }
            }
        }

        .pricingSection {
            margin-bottom: @vw60-580;
            .pricingText {
                font-size: @vw16-580;
                display: flex;
                flex-direction: column;
                gap: @vw10-580;
                .separator {
                    display: none;
                }
            }
        }
    }
}
