// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftTestimonialsBlock {
    .cols {
        display: flex;
        flex-wrap: wrap;
    }
    .sectionHeader {
        position: absolute;
        top: 0;
        left: 0;
        padding-right: @vw100 + @vw30;
    }
    .col {
        position: relative;
        display: inline-block;
        width: 50%;
        vertical-align: top;
    }
    .button {
        margin-top: @vw30;
    }
    .testimonial {
        padding: @vw50;
        .rounded(@vw14);
        background: #FFFFFF;
        display: block;
        opacity: 0;
        .transform(translateY(@vw16));
        &.inview {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            -webkit-transition: opacity .45s, transform .45s;
        }
        &:not(:last-child) {
            margin-bottom: @vw50;
        }
        .meta {
            margin-top: @vw40;
            width: 60%;
            .photo {
                display: inline-block;
                width: @vw76;
                height: @vw76;
                overflow: hidden;
                position: relative;
                vertical-align: middle;
                margin-right: @vw25;
                .rounded(50%);
                img {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                }
            }
            .innerContent {
                display: inline-block;
                vertical-align: middle;
                width: calc(100% ~"-" @vw120);
            }
        }
    }
}

@media all and (max-width: 1080px) {
    .giftTestimonialsBlock {
        .sectionHeader {
            padding-right: @vw100-1080 + @vw30-1080;
        }
        .button {
            margin-top: @vw30-1080;
        }
        .testimonial {
            padding: @vw50-1080;
            .rounded(@vw14-1080);
            .transform(translateY(@vw16-1080));
            &:not(:last-child) {
                margin-bottom: @vw50-1080;
            }
            .meta {
                margin-top: @vw40-1080;
                .photo {
                    width: @vw76-1080;
                    height: @vw76-1080;
                    margin-right: @vw25-1080;
                }
                .innerContent {
                    width: calc(100% ~"-" @vw120-1080);
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .giftTestimonialsBlock {
        .cols {
            flex-direction: column;
        }
        .sectionHeader {
            position: relative;
            padding-right: 0;
            margin-bottom: @vw40-580;
        }
        .col {
            width: 100%;
        }
        .button {
            margin-top: @vw30-580;
        }
        .testimonial {
            padding: @vw40-580;
            .rounded(@vw14-580);
            .transform(translateY(@vw16-580));
            &:not(:last-child) {
                margin-bottom: @vw40-580;
            }
            .meta {
                margin-top: @vw30-580;
                width: 100%;
                .photo {
                    width: @vw60-580;
                    height: @vw60-580;
                    margin-right: @vw20-580;
                }
                .innerContent {
                    width: calc(100% ~"-" @vw80-580);
                }
            }
        }
    }
}