// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftUSPs {
  text-align: center;
  &.inview {
    .improvement {
      opacity: 1;
      .transform(translateY(0));
      transition: opacity .45s, transform .45s;
      -webkit-transition: opacity .45s, transform .45s;
      .stagger(100, 0.15s, 0.15s);
    }
    .stat {
      opacity: 1;
      .transform(translateY(0));
      transition: opacity .45s, transform .45s;
      -webkit-transition: opacity .45s, transform .45s;
      .stagger(100, 0.15s, 0.15s);
    }
  }
  .normalMediumTitle {
    margin-bottom: @vw60;
  }
  .improvements {
      margin: -@vw8 0;
      display: flex;
      width: calc(100% ~"+" @vw16);
      margin-left: -@vw8;
  }
  .improvement {
      border: 1px solid @primaryColor;
      .rounded(@vw14);
      display: inline-block;
      padding: @vw16;
      vertical-align: top;
      width: calc(20% ~"-" @vw16);
      margin: @vw8;
      flex-direction: column;
      opacity: 0;
      .transform(translateY(@vw16));
      .normalTitle {
          margin-bottom: @vw50;
          font-size: @vw32;
          line-height: 1.1;
      }
      .text {
          margin: 0;
      }
      .button {
          margin-top: @vw16;
      }
  }
  img {
    height: @vw90;
    width: auto;
    margin: auto;
    margin-bottom: @vw26;
    display: block;
  }
  .stats {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: @vw60;
    margin-top: @vw80;
    margin-bottom: @vw40;
  }
  .stat {
    flex: 1 1 0;
    max-width: 33%;
    opacity: 0;
    .transform(translateY(@vw16));
    .bigTitle { color: @primaryColor; }
    .text { margin-top: @vw12; }
  }

  .cta { margin-top: @vw30; }
}

@media all and (max-width: 1080px) {
  .giftUSPs {
    .normalMediumTitle {
      margin-bottom: @vw60-1080;
    }
    .improvements {
      margin: -@vw8-1080 0;
      width: calc(100% ~"+" @vw16-1080);
      margin-left: -@vw8-1080;
    }
    .improvement {
      .rounded(@vw14-1080);
      padding: @vw16-1080;
      width: calc(33.333% ~"-" @vw16-1080);
      margin: @vw8-1080;
      .transform(translateY(@vw16-1080));
      .normalTitle {
        margin-bottom: @vw50-1080;
        font-size: @vw32-1080;
      }
      .button {
        margin-top: @vw16-1080;
      }
    }
    img {
      height: @vw90-1080;
      margin-bottom: @vw26-1080;
    }
    .stats {
      gap: @vw60-1080;
      margin-top: @vw80-1080;
      margin-bottom: @vw40-1080;
    }
    .stat {
      .transform(translateY(@vw16-1080));
      .text {
        margin-top: @vw12-1080;
      }
    }
    .cta {
      margin-top: @vw30-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .giftUSPs {
    .normalMediumTitle {
      margin-bottom: @vw60-580;
    }
    .improvements {
      margin: -@vw8-580 0;
      width: calc(100% ~"+" @vw16-580);
      margin-left: -@vw8-580;
      flex-wrap: wrap;
    }
    .improvement {
      .rounded(@vw14-580);
      padding: @vw16-580;
      width: calc(50% ~"-" @vw16-580);
      margin: @vw8-580;
      .transform(translateY(@vw16-580));
      .normalTitle {
        margin-bottom: @vw40-580;
        font-size: @vw28-580;
      }
      .button {
        margin-top: @vw16-580;
      }
    }
    img {
      height: @vw80-580;
      margin-bottom: @vw26-580;
    }
    .stats {
      flex-direction: column;
      gap: @vw40-580;
      margin-top: @vw80-580;
      margin-bottom: @vw40-580;
    }
    .stat {
      max-width: 100%;
      .transform(translateY(@vw16-580));
      display: block;
      margin: auto;
      .text {
        margin-top: @vw12-580;
      }
    }
    .cta {
      margin-top: @vw30-580;
    }
  }
}

