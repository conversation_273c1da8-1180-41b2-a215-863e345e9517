// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftPackageDetails {
    &.inview {
        .cols {
            .col {
                opacity: 1;
                .transform(translateY(0));
                transition: opacity .45s, transform .45s;
                -webkit-transition: opacity .45s, transform .45s;
                .stagger(2, 0.15s, 0.9s);
            }
        }
    }
    .cols {
        display: flex;
        width: calc(100% ~"+" @vw40);
        margin-left: -@vw20;
        .col {
            display: inline-block;
            vertical-align: top;
            width: calc(60% ~"-" @vw40);
            margin: 0 @vw20;
            padding: @vw50;
            .rounded(@vw16);
            background: @hardWhite;
            opacity: 0;
            .transform(translateY(@vw16));
            &.titleCol {
                width: calc(40% ~"-" @vw40);
                display: inline-flex;
                justify-content: space-between;
                flex-direction: column;
            }
        }
        .contentCol {    
            padding-bottom: @vw100 + @vw20;        
            .packageList {
                list-style: none;
                li {
                    position: relative;
                    padding-left: @vw22;
                    margin-bottom: @vw12;
                    color: @secondaryColor;
                    line-height: 1.5;
                    &:before {
                        content: '•';
                        position: absolute;
                        left: 0;
                        top: -@vw5;
                        color: @secondaryColor;
                        font-weight: bold;
                        font-size: 1.2em;
                    }
                    
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
    
    .buttonsWrapper {
        display: flex;
        gap: @vw30;
        .button {
            &.primary {
                // Uses default button styling from constants
            }
            
            &.secondary {
                background: @primaryColor;
                color: @almostWhite;
                
                &:hover {
                    background: darken(@primaryColor, 10%);
                }
            }
        }
    }
    
    @media (max-width: 1080px) {
        .cols {
            gap: @vw16-1080;
            margin-bottom: @vw60-1080;
            
            .titleCol {
                .biggerTitle {
                    margin-bottom: @vw30-1080;
                }
            }
            
            .contentCol {
                .packageList {
                    li {
                        padding-left: @vw25-1080;
                        margin-bottom: @vw15-1080;
                    }
                }
            }
        }
        
        .buttonsWrapper {
            gap: @vw20-1080;
        }
    }
    
    @media (max-width: 580px) {
        .cols {
            gap: @vw40-580;
            margin-bottom: @vw40-580;
            flex-direction: column;
            width: 100%;
            margin-left: 0;
            .col {
                padding: @vw32-580;
                margin: 0 !important;
                width: 100% !important;
            }
            .titleCol {
                .biggerTitle {
                    margin-bottom: @vw20-580;
                }
            }
            
            .contentCol {
                padding-bottom: @vw100-580 + @vw30-580;
                .packageList {
                    li {
                        padding-left: @vw20-580;
                        margin-bottom: @vw12-580;
                    }
                }
            }
        }
        .button {
            margin-top: @vw22-580;
        }
        .buttonsWrapper {
            flex-direction: column;
            gap: @vw15-580;
            
            .button {
                text-align: center;
            }
        }
    }
}
