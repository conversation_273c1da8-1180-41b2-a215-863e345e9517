// out: false

.events-archive {

  .eventsListingBlock {
    padding: 80px 0;

    .events-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 40px;
      margin-bottom: 60px;

      .event-item {
        background: @hardWhite;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .event-link {
          display: block;
          text-decoration: none;
          color: inherit;
          height: 100%;
        }

        .event-image {
          position: relative;
          width: 100%;
          height: 200px;
          overflow: hidden;

          .event-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
          }
        }

        &:hover .event-thumbnail {
          transform: scale(1.05);
        }

        .event-content {
          padding: 24px;

          .event-date {
            margin-bottom: 12px;
            font-size: 14px;
            font-weight: 600;
            color: @primaryColor;
          }

          .event-title {
            margin-bottom: 16px;
            font-size: 20px;
            line-height: 1.3;
            color: @almostBlack;
            transition: color 0.3s ease;
          }

          .event-location {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
            color: @grey;

            i {
              margin-right: 8px;
              font-size: 12px;
            }
          }

          .event-excerpt {
            font-size: 14px;
            line-height: 1.5;
            color: @grey;

            p {
              margin: 0;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }

        &:hover .event-title {
          color: @primaryColor;
        }
      }
    }

    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 60px;

      .pagination {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 8px;

        li {
          a, span {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;

            &.current {
              background: @primaryColor;
              color: @hardWhite;
            }

            &:not(.current) {
              background: @borderGrey;
              color: @almostBlack;

              &:hover {
                background: @primaryColor;
                color: @hardWhite;
              }
            }
          }
        }
      }
    }

    .no-events {
      text-align: center;
      padding: 80px 40px;

      h2 {
        font-size: 32px;
        margin-bottom: 16px;
        color: @almostBlack;
      }

      p {
        font-size: 16px;
        color: @grey;
        margin: 0;
      }
    }
  }
}

// Responsive adjustments
@media all and (max-width: 1080px) {
  .events-archive {
    .eventsListingBlock {
      padding: 60px 0;

      .events-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;

        .event-item {
          .event-image {
            height: 180px;
          }

          .event-content {
            padding: 20px;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .events-archive {
    .eventsListingBlock {
      padding: 40px 0;

      .events-grid {
        grid-template-columns: 1fr;
        gap: 20px;

        .event-item {
          .event-image {
            height: 160px;
          }

          .event-content {
            padding: 16px;

            .event-title {
              font-size: 18px;
            }
          }
        }
      }

      .pagination-wrapper {
        margin-top: 40px;

        .pagination {
          gap: 4px;

          li a, li span {
            width: 36px;
            height: 36px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
