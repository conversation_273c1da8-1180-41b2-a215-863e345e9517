// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftCredentials {
    &.inview {
        .credential {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            -webkit-transition: opacity .45s, transform .45s;
            .stagger(100, 0.15s, 0.9s);
        }
    }
    .mediumTitle {
        text-align: center;
        margin-bottom: @vw100;
    }
    .credentials {
        margin: -@vw8 0;
        display: flex;
        width: calc(100% ~"+" @vw16);
        margin-left: -@vw8;
    }
    .credential {
        border: 1px solid @primaryColor;
        .rounded(@vw14);
        display: inline-block;
        padding: @vw16;
        vertical-align: top;
        width: calc(20% ~"-" @vw16);
        margin: @vw8;
        flex-direction: column;
        opacity: 0;
        .transform(translateY(@vw16));
        .normalTitle {
            margin-bottom: @vw50;
        }
        .text {
            margin: 0;
        }
        .button {
            margin-top: @vw16;
        }
    }
}

@media all and (max-width: 1080px) {
    .giftCredentials {
        .mediumTitle {
            margin-bottom: @vw100-1080;
        }
        .credentials {
            margin: -@vw8-1080 0;
            width: calc(100% ~"+" @vw16-1080);
            margin-left: -@vw8-1080;
            flex-wrap: wrap;
        }
        .credential {
            .rounded(@vw14-1080);
            padding: @vw16-1080;
            width: calc(50% ~"-" @vw16-1080);
            margin: @vw8-1080;
            .transform(translateY(@vw16-1080));
            .normalTitle {
                margin-bottom: @vw50-1080;
            }
            .button {
                margin-top: @vw16-1080;
            }
        }
    }
}

@media all and (max-width: 580px) {
    .giftCredentials {
        .mediumTitle {
            margin-bottom: @vw100-580;
        }
        .credentials {
            margin: -@vw8-580 0;
            width: calc(100% ~"+" @vw16-580);
            margin-left: -@vw8-580;
            flex-direction: column;
        }
        .credential {
            .rounded(@vw14-580);
            padding: @vw20-580;
            width: calc(100% ~"-" @vw16-580);
            margin: @vw8-580;
            .transform(translateY(@vw16-580));
            .normalTitle {
                margin-bottom: @vw30-580;
            }
            .button {
                margin-top: @vw16-580;
            }
        }
    }
}