// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftSignupBlock {
    position: relative;
    .backgroundImage {
        position: absolute;
        bottom: -(@vw100 * 2.2);
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1));
        -webkit-mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1));
        video, img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: bottom;
            position: absolute;
            bottom: 0;
            left: 0;
        }
    }
    .cols {
        display: flex;
        gap: @vw16;
    }
    .col {
        width: 100%;
        display: inline-flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        .sectionHeader {
            margin-bottom: @vw50;
            text-align: center;
        }
    }
    form {
        .mediumTitle {
            display: none;
        }
    }
}

@media all and (max-width: 1080px) {
    .giftSignupBlock {
        .backgroundImage {
            bottom: -(@vw100-1080 * 2.2);
        }
        .cols {
            gap: @vw16-1080;
        }
        .col {
            .sectionHeader {
                margin-bottom: @vw50-1080;
            }
        }
    }
}

@media all and (max-width: 580px) {
  .giftSignupBlock {
        .backgroundImage {
            bottom: -(@vw100-580 * 1.8);
        }
        .cols {
            gap: @vw16-580;
        }
        .col {
            .sectionHeader {
                margin-bottom: @vw50-580;
            }
        }
    }
}