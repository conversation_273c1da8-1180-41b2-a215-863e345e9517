// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftMediaGrid {
    .items {
        display: flex;
        flex-wrap: wrap;
        gap: @vw16;
    }
    .item {
        width: calc(50% ~"-" @vw8);
        display: inline-block;
        vertical-align: top;
        &:nth-child(odd) {
            text-align: right;
            padding-right: @vw100 * 2;
        }
        &:nth-child(even) {
            margin: @vw100 * 2.7 0 @vw100 0;
        }
        &:nth-child(1) {
            .imageWrapper {
                width: @vw100 * 4;
                .innerImage {
                    .paddingRatio(388,455);
                }
            }
        }
        &:nth-child(2) {
            .imageWrapper {
                width: @vw100 * 5.9;
                .innerImage {
                    .paddingRatio(590,455);
                }
            }
        }
        &:nth-child(3) {
            .imageWrapper {
                width: @vw100 * 5.9;
                .innerImage {
                    .paddingRatio(590,628);
                }
            }
        }
        .imageWrapper {
            overflow: hidden;
            position: relative;
            .rounded(@vw14);
            .innerImage {
                position: relative;
                top: 0;
                left: 0;
                width: 100%;
                height: 0;
            }
            img, video {
                position: absolute;
                top: 50%;
                left: 50%;
                .transform(translate(-50%, -50%));
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
    }
}

@media all and (max-width: 1080px) {
    .giftMediaGrid {
        .items {
            gap: @vw16-1080;
        }
        .item {
            width: calc(50% ~"-" @vw8-1080);
            &:nth-child(odd) {
                padding-right: @vw100-1080;
            }
            &:nth-child(even) {
                margin: @vw100-1080 * 1.5 0 @vw50-1080 0;
            }
            &:nth-child(1) {
                .imageWrapper {
                    width: @vw100-1080 * 4;
                }
            }
            &:nth-child(2) {
                .imageWrapper {
                    width: @vw100-1080 * 5.9;
                }
            }
            &:nth-child(3) {
                .imageWrapper {
                    width: @vw100-1080 * 5.9;
                }
            }
            .imageWrapper {
                .rounded(@vw14-1080);
            }
        }
    }
}

@media all and (max-width: 580px) {
    .giftMediaGrid {
        .items {
            gap: @vw16-580;
            flex-direction: column;
        }
        .item {
            width: 100%;
            text-align: center !important;
            &:nth-child(odd) {
                padding-right: 0;
            }
            &:nth-child(even) {
                margin: @vw40-580 0;
            }
            &:nth-child(1) {
                .imageWrapper {
                    width: 100%;
                }
            }
            &:nth-child(2) {
                .imageWrapper {
                    width: 100%;
                }
            }
            &:nth-child(3) {
                .imageWrapper {
                    width: 100%;
                }
            }
            .imageWrapper {
                .rounded(@vw14-580);
                margin: 0 auto;
            }
        }
    }
}