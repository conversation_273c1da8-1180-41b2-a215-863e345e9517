<?php
$side = get_field('image_side'); // left or right
$image = get_field('image');
$title = get_field('title');
$text = get_field('text');
$smallerTitle = get_field('smaller_title');
?>
<section class="giftMediaTextBlock smaller side-<?= esc_attr($side?:'left') ?> <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper smaller">
    <div class="cols">
      <div class="media">
        <div class="innerWrapper">
          <?php if($image): $i=optimize_images_for_compressx($image); ?><img class="lazy" data-parallax data-parallax-speed="1" data-src="<?= esc_url($i['sizes']['large'] ?? $i['url']) ?>" alt=""><?php endif; ?>
        </div>
      </div>
      <div class="info">
        <?php if($title):?>
          <? if ($smallerTitle): ?>
            <h2 class="normalTitle"><?= esc_html($title) ?></h2>
          <? else: ?>
            <h2 class="mediumTitle"><?= esc_html($title) ?></h2>
          <?php endif; ?>
        <?php endif; ?>
        <?php if($text):?><div class="text"><?= wpautop(wp_kses_post($text)) ?></div><?php endif; ?>
          <?php if(get_field('button')): ?>
           <a class="bigButton" href="<?= esc_url(get_field('button')['url']) ?>" title="<?= esc_attr(get_field('button')['title']) ?>" target="_blank">
            <span class="textTitle"><?= esc_html(get_field('button')['title']) ?></span>
          </a>
          <?php endif; ?>
      </div>
    </div>
  </div>
</section>

