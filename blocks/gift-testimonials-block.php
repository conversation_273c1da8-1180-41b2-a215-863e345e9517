<?php
$categories = get_field('testimonial_categories');
$limit = get_field('limit') ?: 3;

// Build query args
$query_args = array(
  'post_type' => 'testimonial',
  'posts_per_page' => $limit,
  'orderby' => 'rand',
  'order' => 'random',
);

// Add category filter if categories are selected
if ($categories && !empty($categories)) {
    $query_args['tax_query'] = array(
        array(
            'taxonomy' => 'testimonial_category',
            'field'    => 'term_id',
            'terms'    => $categories,
            'operator' => 'IN'
        )
    );
}

$testimonials = get_posts($query_args);

// If no testimonials found with selected categories, get random testimonials without category filter
if (empty($testimonials) && $categories && !empty($categories)) {
    $fallback_args = array(
        'post_type' => 'testimonial',
        'posts_per_page' => $limit,
        'orderby' => 'rand',
        'order' => 'random',
    );
    $testimonials = get_posts($fallback_args);
}
?>
<section class="giftTestimonialsBlock <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <div class="col">
        <div class="sectionHeader" data-sticky-top>
          <h2 class="bigTitle" data-lines data-words><?php the_field('title') ?></h2>
          <div class="text"><p><?php the_field('text') ?></p></div>
          <?php if(get_field('button')): render_button_from_array(get_field('button'), "outline"); endif; ?>
        </div>
    </div>
    <div class="col">
      <?php foreach($testimonials as $t): setup_postdata($t); $photo=get_field('testimonial_photo', $t->ID); ?>
        <article class="testimonial" data-init>
          <h3 class="normalTitle">
              <?php echo get_the_title($t->ID); ?>
          </h3>
          <div class="quote text"><p><?php the_field('testimonial_quote', $t->ID); ?></p></div>
          <div class="meta">
            <div class="photo"><?php if($photo): $i=optimize_images_for_compressx($photo); ?><img class="lazy" data-src="<?= esc_url($i['sizes']['thumbnail'] ?? $i['url']) ?>" alt="<?php echo esc_attr(get_the_title($t->ID)) ?>"><?php endif; ?></div>
            <div class="innerContent">
              <h4 class="textTitle primary"><?php the_field('testimonial_name', $t->ID); ?></h4>
              <h5 class="textTitle"><?php the_field('testimonial_role', $t->ID); ?></h5>
            </div>
          </div>
        </article>
        <?php wp_reset_postdata(); ?>
      <?php endforeach; ?>
    </div>
  </div>
</section>

