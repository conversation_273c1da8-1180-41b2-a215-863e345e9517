<?php
$categories = get_field('testimonial_categories');
$limit = get_field('limit') ?: 3;

// Build query args
$query_args = array(
  'post_type' => 'testimonial',
  'posts_per_page' => 1,
  'orderby' => 'rand',
  'order' => 'random',
);

// Add category filter if categories are selected
if ($categories && !empty($categories)) {
    $query_args['tax_query'] = array(
        array(
            'taxonomy' => 'testimonial_category',
            'field'    => 'term_id',
            'terms'    => $categories,
            'operator' => 'IN'
        )
    );
}

$testimonials = get_posts($query_args);

// If no testimonials found with selected categories, get random testimonials without category filter
if (empty($testimonials) && $categories && !empty($categories)) {
    $fallback_args = array(
        'post_type' => 'testimonial',
        'posts_per_page' => $limit,
        'orderby' => 'rand',
        'order' => 'random',
    );
    $testimonials = get_posts($fallback_args);
}
?>
<section class="giftTestimonialHighlightBlock <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper smallest">
    <div class="cols">
        <?php foreach($testimonials as $t): setup_postdata($t); $photo=get_field('testimonial_photo', $t->ID); ?>
        <div class="col leftCol">
          <div class="imageWrapper">
            <div class="innerImage">
              <?php if($photo): $i=optimize_images_for_compressx($photo); ?><img class="lazy" data-src="<?= esc_url($i['sizes']['medium'] ?? $i['url']) ?>" alt="<?php echo esc_attr(get_the_title($t->ID)) ?>"><?php endif; ?>
            </div>
          </div>
        </div>
        <div class="col">
          <article class="testimonial" data-init>
            <h3 class="normalTitle" data-lines data-words>
                <?php echo get_the_title($t->ID); ?>
            </h3>
            <div class="meta">
              <div class="innerContent">
                <h4 class="textTitle smaller"><?php the_field('testimonial_name', $t->ID); ?>, ><?php the_field('testimonial_role', $t->ID); ?></h5>
              </div>
            </div>
          </article>
      </div>
      <?php wp_reset_postdata(); ?>
      <?php endforeach; ?>
    </div>
  </div>
</section>

