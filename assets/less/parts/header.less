
// out: false
@import '../vw_values.less';
@import '../constants.less'; 
header {
    position: fixed;
    padding-top: @vw16;
    top: 0;
    width: 100%;
    left: 0;
    z-index: 99;
    .transform(translateY(0));
    .transitionMore(transform, .3s, 0s, ease-in-out);
    &.hidden {
        .transform(translateY(-@vw90));
    }
    &.scrolled {
      .background {
        height: @vw90;
        background: rgba(@secondaryColor, .24);
        mask-image: none;
        -webkit-mask-image: none;
      }
      .col {
        .logo {
          height: @vw50;
          .transitionMore(height, .3s);
          svg {
            path, rect {
                fill: @almostWhite;
                .transition(.15s);
            }
          }
        }
      }
    }
    #headerContainer {
      &.active {
        .innerMenu {
          a {
            &[aria-current="page"] {
              color: @primaryColor;
              &:before {
                .transform(scaleX(1));
                .transitionMore(transform, .3s, .15s, ease-in-out);
              }
            }
          }
        }
      }
    }
    > .background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: @vw100 * 2;
      pointer-events: none;
      z-index: -1;
      backdrop-filter: blur(@vw50); // Pas de blur-waarde aan naar wens
      -webkit-backdrop-filter: blur(10px); // Voor Safari-ondersteuning
      background: rgba(0,0,0,0); // Zorg ervoor dat de achtergrond deels transparant is
      -webkit-mask-image: linear-gradient(rgba(0,0,0,1), rgba(0,0,0,0));
      mask-image: linear-gradient(rgba(0,0,0,1), rgba(0,0,0,0));
    }
    .innerMenu {
        display: inline-block;
        vertical-align: middle;
        list-style: none;
        li {
          display: inline-block;
          &:not(:last-of-type) {
              margin-right: @vw35;
          }
          &:last-child {
            a {
              padding-right: 0;
            }
          }
        }
        a {
            display: inline-block;
            vertical-align: middle;
            cursor: pointer;
            color: @almostWhite;
            text-decoration: none;
            transition: color .3s, transform .3s;
            padding-left: 0;
            font-size: @vw16;
            letter-spacing: .14em;
            text-transform: uppercase;
            transition: padding-left .3s, padding-right .3s, color .3s;
            -webkit-transition: padding-left .3s, padding-right .3s, color .3s;
            &:before {
              content: '';
              position: absolute;
              height: 1px;
              width: 100%;
              top: auto;
              bottom: -@vw3;
              .transform(scaleX(0));
              display: inline-block;
              background: @primaryColor;
              vertical-align: middle;
              left: 0;
              .transitionMore(transform, .15s, 0s, ease-in-out);
            }
            &:hover {
                color: @primaryColor;
            }
        }
    }
    .col {
        display: inline-block;
        width: 40%;
        vertical-align: middle;
        position: relative;
        &:nth-child(2) {
          text-align: center;
          width: 20%;
        }
        &:last-child {
            text-align: right;
        } 
        .logo {
            display: inline-block;
            width: auto;
            height: @vw80;
            vertical-align: middle;
            position: relative;
            opacity: 1;
            cursor: pointer;
            .transition(.3s);
            &:hover {
                opacity: .4;
            }
            * {
              cursor: pointer;
            }
            svg {
              height: 100%;
              width: auto;
              object-fit: contain;
            }
        }
        .hamburger {
          display: none;
        }
        .button {
            display: inline-block;
            vertical-align: middle;
            &:not(:last-child) {
              margin-right: @vw10;
            }
        }
    }
}

.mainMenu {
  display: none;
}

@media all and (max-width: 1080px) {
  .mainMenu {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100lvh;
    background: @hardWhite;
    z-index: 98;
    padding: @vw100-1080 + @vw50-1080 @vw50-1080;
    opacity: 0;
    pointer-events: none;
    &.active {
      opacity: 1;
      pointer-events: all;
      li {
        .transform(translateY(0));
        opacity: 1;
        .stagger(30, 0.15s, .03s);
      }
    }
    .innerContent {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    li {
      display: block;
      .transform(translateY(@vw30-1080));
      opacity: 0;
      transition: transform .3s, opacity .3s;
      margin-bottom: @vw10-1080;
      font-family: "Albra",sans-serif;
      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: @primaryColor;
        .transform(scaleX(0));
        .transitionMore(transform, .3s, 0s, ease-in-out);
      }
      &:has(a[aria-current]) {
        a {
          color: @primaryColor;
        }
        &:after {
          .transform(scaleX(1));
        }
      }
      a {
        font-size: @vw50-1080;
        color: @backgroundColor;
        text-decoration: none;
        margin-bottom: @vw16-1080;
      }
    }
    .socials {
      margin-top: auto;
      .socialLink {
        margin-right: @vw16-1080;
        background: @primaryColor;
        color: @hardWhite;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  body {
    &:has(.mainMenu.active) {
      header {
        > .background {
          opacity: 0;
        }
        .logo {
          path, rect {
            &:nth-child(9), &:nth-child(10), &:nth-child(11), &:nth-child(12) {
              fill: @primaryColor;
              .transition(.15s);
            }
          }
        }
        .col {
          .hamburger {
            .border {
              &:nth-child(1) {
                .transform(translateY(@vw6-1080) rotate(-45deg));
              }
              &:nth-child(2) {
                width: 0%;
              }
              &:nth-child(3) {
                .transform(translateY(-@vw8-1080) rotate(45deg));
              }
            }
          }
        }
      }
    }
  }
  header {
    padding-top: @vw16-1080;
    .transitionMore(transform, .3s, 0s, ease-in-out);
    &.hidden {
        .transform(translateY(-@vw90-1080));
    }
    &.scrolled {
      .background {
        height: @vw90-1080;
        background: rgba(@secondaryColor, .24);
        mask-image: none;
        -webkit-mask-image: none;
      }
      .col {
        .logo {
          height: @vw50-1080;
          .transitionMore(height, .3s);
          svg {
            path, rect {
                fill: @almostWhite;
                .transition(.15s);
            }
          }
        }
      }
    }
    #headerContainer {
      &.active {
        .innerMenu {
          a {
            &[aria-current="page"] {
              padding-left: @vw16-1080;
            }
          }
        }
      }
    }
    > .background {
      height: @vw100-1080 * 3;
      backdrop-filter: blur(@vw50-1080); // Pas de blur-waarde aan naar wens
      -webkit-backdrop-filter: blur(10px); // Voor Safari-ondersteuning
    }
    .innerMenu {
        a {
            padding-right: @vw40-1080;
            &:before {
              height: @vw6-1080;
              width: @vw6-1080;
            }
            &:not(:last-of-type) {
                margin-right: @vw16-1080;
            }
        }
    }
    .col {
        width: 50%;
        &:nth-child(1) {
          display: none;
        }
        &:nth-child(2) {
          text-align: left;
          width: 50%;
        }
        &:last-child {
            width: 50%;
        } 
        .logo {
            width: @vw100-1080 * 2;
        }
        .hamburger {
          cursor: pointer;
          display: inline-block;
          position: relative;
          height: @vw50-1080;
          width: @vw50-1080;
          background: @primaryColor;
          .rounded(@vw12-1080);
          * {
            cursor: pointer;
          }
          .innerBurger {
            position: absolute;
            top: 50%;
            left: 50%;
            width: @vw16-1080;
            height: @vw16-1080;
            .transform(translate(-50%, -50%));
          }
          .border {
            position: absolute;
            display: block;
            height: 2px;
            width: 100%;
            border-radius: @vw2-1080;
            background: @hardWhite;
            .transition(.3s);
            &:after {
              content: '';
              position: absolute;
              background: @hardWhite;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              .transform(scaleX(0));
            }
            &:first-child {
              top: 0;
            }
            &:nth-child(2) {
              top: 50%;
              transform: translateY(-50%);
            }
            &:nth-child(3) {
              bottom: 0;
              top: auto;
            }
          }
        }
        .button {
            display: none;
        }
    }
  }
}

@media all and (max-width: 580px) {
  .mainMenu {
    padding: @vw100-580 + @vw50-580 @vw16-580;
    li {
      .transform(translateY(@vw30-580));
      margin-bottom: @vw16-580;
      a {
        font-size: @vw60-580;
        margin-bottom: @vw32-580;
      }
    }
    .socials {
      .socialLink {
        margin-right: @vw16-580;
      }
    }
  }
  body {
    &:has(.mainMenu.active) {
      header {
        .col {
          .hamburger {
            .border {
              &:nth-child(1) {
                .transform(translateY(@vw9-580) rotate(-45deg));
              }
              &:nth-child(3) {
                .transform(translateY(-@vw10-580) rotate(45deg));
              }
            }
          }
        }
      }
    }
  }
  header {
    padding-top: @vw16-580;
    .transitionMore(transform, .3s, 0s, ease-in-out);
    &.hidden {
        .transform(translateY(-@vw100-580));
    }
    &.scrolled {
      .background {
        height: @vw100-580;
      }
      .col {
        .logo {
          height: @vw50-580;
          svg {
            path, rect {
                fill: @almostWhite;
            }
          }
        }
      }
    }
    #headerContainer {
      &.active {
        .innerMenu {
          a {
            &[aria-current="page"] {
              padding-left: @vw16-580;
            }
          }
        }
      }
    }
    > .background {
      height: @vw100-580 * 3;
      backdrop-filter: blur(@vw50-580); 
      -webkit-backdrop-filter: blur(10px);
    }
    .innerMenu {
        a {
            padding-right: @vw40-580;
            &:before {
              height: @vw6-580;
              width: @vw6-580;
            }
            &:not(:last-of-type) {
                margin-right: @vw16-580;
            }
        }
    }
    .col {
        .logo {
            width: @vw100-580 * 2;
            height: @vw44-580;
        }
        .hamburger {
          height: @vw60-580;
          width: @vw60-580;
          .rounded(@vw12-580);
          .innerBurger {
            width: @vw23-580;
            height: @vw23-580;
          }
          .border {
            border-radius: @vw2-580;
            .transition(.3s);
          }
        }
    }
  }
}
