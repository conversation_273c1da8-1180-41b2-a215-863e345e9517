<!DOCTYPE html>
<html lang="nl" dir="ltr">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title><?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?></title>
  <meta name="robots" content="follow, index, max-snippet:-1, max-video-preview:-1, max-image-preview:large">
  <meta name="msapplication-TileColor" content="#00aba9">
  <meta name="theme-color" content="#ffffff">
  <meta name="description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="author" content="https://www.linkedin.com/in/dennisthemenace/"/>
  <meta property="og:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>" />
  <meta property="og:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>" />
  <meta property="og:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>" />
  <meta property="og:image:alt" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-title')); ?>" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:image:type" content="image/jpeg" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>" />
  <meta property="og:site_name" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?>" />
  <meta property="og:locale" content="nl" />
  <meta name="DC.title" content="<?php echo the_title(); ?>">
  <meta name="DC.creator" content="Door Dennis">
  <meta name="DC.subject" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="DC.description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="DC.publisher" content="<?php echo get_bloginfo('name'); ?>">
  <meta name="DC.language" content="nl">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>">
  <meta name="twitter:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="twitter:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>">
  <link rel="canonical" href="<?php echo esc_url(get_permalink()); ?>" />
  <link rel="icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
  <link rel="shortcut icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://use.typekit.net/sui0kvj.css">
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "<?php echo get_bloginfo('name'); ?>",
    "url": "<?php echo esc_url(home_url()); ?>",
    "logo": "<?php echo get_stylesheet_directory_uri(); ?>/logo.png",
    "description": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-description')); ?>",
    "image": "<?php echo esc_url(get_theme_mod('customTheme-main-callout-featured-image')); ?>",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
      "contactType": "customer service",
      "contactOption": "TollFree",
      "areaServed": "NL",
      "availableLanguage": "Dutch"
    },
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-address')); ?>"
    },
    "sameAs": [
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-tiktok')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram')); ?>"
    ],
    "email": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-mail')); ?>",
    "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
    "additionalProperty": [
      {
        "@type": "PropertyValue",
        "name": "Company Information",
        "value": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-company-information')); ?>"
      },
      {
        "@type": "PropertyValue",
        "name": "Analytics ID",
        "value": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-analytics')); ?>"
      }
    ]
  }
  </script>

  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', '<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>', {
      'anonymize_ip': true
    });
  </script>

  <?php wp_head(); ?>
</head>
<?php if(get_theme_mod("customTheme-main-callout-analytics")) { ?>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>"></script>
  
<?php } ?>
  <body class="no-scroll">
    <header>
      <div class="background"></div>
      <div class="contentWrapper">
        <div class="col" id="headerContainer">
          <div class="innerMenu">
              <?php wp_nav_menu( array(
                  'theme_location' => 'primary-menu',
              ) ); ?>
          </div>
        </div>
        <div class="col">
            <a href="/" title="Logo | <?php echo get_theme_mod('customTheme-main-callout-title') ?>" class="logo">
              <svg data-name="Group 199" width="110" height="78.875" viewBox="0 0 110 78.875">
                <defs>
                  <clipPath id="clip-path">
                    <rect id="Rectangle_269" data-name="Rectangle 269" width="110" height="78.875" fill="none"/>
                  </clipPath>
                </defs>
                <g data-name="Group 198" clip-path="url(#clip-path)">
                  <path data-name="Path 114" d="M150.9,256.165l-3.631-.347-16.6-1.593-5.161-.494a55.57,55.57,0,0,0,4.93,3.978q1.741,1.251,3.58,2.365,1.456.885,2.971,1.679.4.213.807.415,1.547.782,3.147,1.466.883.38,1.782.727c.***********.6.23l2.962-1.737,4.124-2.417.459-.27-.005-.014c-.018-.115-.034-.22-.047-.321-.021-.162-.037-.3-.049-.436-.011-.113-.02-.221-.027-.33-.018-.285-.027-.55-.027-.81v-.01a12.22,12.22,0,0,1,.182-2.081" transform="translate(-107.832 -217.992)" fill="#be9b72"/>
                  <path data-name="Path 115" d="M475.355,259.066c-.007.107-.015.214-.026.326-.012.128-.027.261-.045.4-.016.124-.059.368-.059.368l.317.186,4.269,2.5,2.962,1.737c.2-.075.4-.151.6-.23q.923-.357,1.83-.747,1.576-.676,3.1-1.447.406-.2.807-.415,1.513-.794,2.97-1.679,1.838-1.113,3.58-2.365a55.565,55.565,0,0,0,4.93-3.978l-5.161.494-16.6,1.593-3.631.347a12.228,12.228,0,0,1,.182,2.081v.01c0,.26-.009.525-.027.811" transform="translate(-408.27 -217.992)" fill="#be9b72"/>
                  <path data-name="Path 116" d="M197.7,14.624a32.393,32.393,0,0,1,41.652,0c.348.29.689.59,1.024.9l6.341-3.661a39.419,39.419,0,0,0-56.382,0l6.341,3.661c.335-.306.676-.606,1.024-.9" transform="translate(-163.531)" fill="#be9b72"/>
                  <path data-name="Path 117" d="M59.473,45.2,55,52.12,50.527,45.2,0,15.7A55.213,55.213,0,0,0,3.769,28.992l43.24,18.272L47,47.271a9.46,9.46,0,0,0-.546.842,9.6,9.6,0,0,0-.9,2.085,9.378,9.378,0,0,0-.241.975,9.919,9.919,0,0,0-.176,1.865v.01c0,.208.023.666.023.666.006.087.013.173.021.261.011.121.025.241.041.361.018.142.041.283.065.423a9.963,9.963,0,0,0,.9,2.7L55,73.079,63.814,57.46a9.963,9.963,0,0,0,.9-2.7c.027-.151.049-.3.069-.456.014-.108.027-.218.037-.327.008-.087.044-.711.044-.927v-.01a9.919,9.919,0,0,0-.176-1.865,9.379,9.379,0,0,0-.241-.975,9.67,9.67,0,0,0-.882-2.054A9.329,9.329,0,0,0,63,47.271l-.005-.007L83.967,38.4l6.5-2.747,15.765-6.666A55.212,55.212,0,0,0,110,15.7Z" transform="translate(0 -13.489)" fill="#be9b72"/>
                  <path data-name="Path 118" d="M63.383,166.4l18.138,3.556,1.49.292.328.064c.02-.072.041-.143.062-.214a11.861,11.861,0,0,1,.888-2.167l-2.584-.872-16.771-5.664-6.683-2.256L46.56,155.19a55.613,55.613,0,0,0,6.51,9.185l3.4.666Z" transform="translate(-40.002 -133.332)" fill="#be9b72"/>
                  <path data-name="Path 119" d="M486.009,161.692l-16.8,5.673-2.551.86a11.961,11.961,0,0,1,.872,2.135c.022.071.042.144.063.216l.328-.064,1.49-.292,18.138-3.556,6.914-1.356,3.4-.666a55.628,55.628,0,0,0,6.485-9.141l-11.654,3.935Z" transform="translate(-400.93 -133.598)" fill="#be9b72"/>
                  <path data-name="Path 120" d="M183.483,306.323q-.79.49-1.594.952a.041.041,0,0,1,0,.013q-.285,1.037-.637,2.044-.332.951-.72,1.876a32.415,32.415,0,0,1-27.33,19.758q-1.261.1-2.549.1t-2.549-.1a32.415,32.415,0,0,1-27.33-19.758q-.389-.923-.72-1.876-.346-.991-.627-2.011a.237.237,0,0,1-.013-.045q-.8-.463-1.594-.952-1.257-.773-2.475-1.607-2.068-1.413-4.011-2.986.142,1.868.456,3.682a38.976,38.976,0,0,0,.875,3.887c.025.093.052.186.079.277q.363,1.27.811,2.5.5,1.384,1.1,2.717a39.451,39.451,0,0,0,72,0q.6-1.333,1.1-2.717.452-1.249.821-2.535c.023-.082.046-.163.069-.245a38.976,38.976,0,0,0,.875-3.887q.313-1.813.456-3.682-1.942,1.572-4.011,2.986-1.217.832-2.475,1.607" transform="translate(-95.649 -259.232)" fill="#be9b72"/>
                </g>
              </svg>
            </a>
        </div>
        <div class="col">
          <div id="burger" class="hamburger"><div class="innerBurger"><span class="border"></span><span class="border"></span><span class="border"></span></div></div>
          <?php
            $button = get_field('header_button', 'option');
            $shop_link = get_field('shop_link', 'option'); // external URL
          ?>
          <?php if ($shop_link): ?>
            <a class="button" title="ONLINE STORE" href="<?= esc_url($shop_link) ?>" target="_blank">
              <span class="hiddenText">ONLINE STORE</span>
              <span class="innerTexts">
                <span class="innerText" aria-hidden="true" data-words>ONLINE STORE</span>
                <span class="innerText" aria-hidden="true" data-words>ONLINE STORE</span>
              </span>
            </a>
          <?php endif; ?>
          <?php if ($button): ?>
            <?php render_button_from_array($button, "primary"); ?>
          <?php endif; ?>
        </div>
      </div>
    </header>
    <div id="menu" class="mainMenu">
      <div class="background"></div>
      <div id="menuContainer" class="innerContent">
        <?php wp_nav_menu( array(
            'theme_location' => 'footer-menu-1',
        ) ); ?>
        <div class="socials">
          <?php if (get_theme_mod('customTheme-main-callout-facebook')) : ?>
            <a href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook')); ?>" title="Facebook" target="_blank" class="socialLink">
              <i class="icon-facebook"></i>
            </a>
          <?php endif; ?>
          <?php if (get_theme_mod('customTheme-main-callout-linkedin')) : ?>
            <a href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin')); ?>" title="LinkedIn" target="_blank" class="socialLink">
              <i class="icon-linkedin"></i>
            </a>
          <?php endif; ?> 
          <?php if (get_theme_mod('customTheme-main-callout-tiktok')) : ?>
            <a href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-tiktok')); ?>" title="Tiktok" target="_blank" class="socialLink">
              <i class="icon-tiktok"></i>
            </a>
          <?php endif; ?>
          <?php if (get_theme_mod('customTheme-main-callout-instagram')) : ?>
            <a href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram')); ?>" title="Instagram" target="_blank" class="socialLink">
              <i class="icon-instagram"></i>
            </a>
          <?php endif; ?>
        </div>
      </div>
    </div>
  <div class="backgroundCursor"></div>
  <div id="pageContainer" class="transition-fade blocks">
   <div class="innerBlocks">