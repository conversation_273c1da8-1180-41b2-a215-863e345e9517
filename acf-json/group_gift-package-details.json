{"key": "group_gift-package-details", "title": "Gift Package Details Fields", "fields": [{"key": "field_gift-package-details_title", "label": "Title", "name": "title", "type": "text", "required": 1}, {"key": "field_gift-package-details_items", "label": "Package Items", "name": "package_items", "type": "repeater", "layout": "table", "button_label": "Add Item", "sub_fields": [{"key": "field_gift-package-details_item_text", "label": "Item Text", "name": "item_text", "type": "text", "required": 1}]}, {"key": "field_gift-package-details_primary_button", "label": "Primary Button", "name": "primary_button", "type": "link", "return_format": "array"}, {"key": "field_gift-package-details_secondary_button", "label": "Secondary Button", "name": "secondary_button", "type": "link", "return_format": "array"}], "location": [[{"param": "block", "operator": "==", "value": "acf/gift-package-details"}]], "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "active": true}