{"key": "group_gift-testimonial-highlight", "title": "Gift Testimonial Highlight Fields", "fields": [{"key": "field_gift-testimonial-highlight_limit", "label": "Limit", "name": "limit", "aria-label": "", "type": "number", "instructions": "Number of testimonials to show (default: 3)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": 3, "min": 1, "max": 10, "step": 1, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_gift-testimonial-highlight_categories", "label": "Testimonial Categories", "name": "testimonial_categories", "aria-label": "", "type": "taxonomy", "instructions": "Select specific categories to filter testimonials. Leave empty to show from all categories.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "taxonomy": "testimonial_category", "field_type": "multi_select", "allow_null": 1, "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "multiple": 1, "bidirectional_target": []}], "location": [[{"param": "block", "operator": "==", "value": "acf/gift-testimonial-highlight"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1756491847}