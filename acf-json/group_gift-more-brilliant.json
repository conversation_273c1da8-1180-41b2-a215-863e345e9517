{"key": "group_gift-more-brilliant", "title": "Gift More Brilliant Block Fields", "fields": [{"key": "field_more_brilliant_title", "label": "Title", "name": "title", "aria-label": "", "type": "wysiwyg", "instructions": "Main title (e.g., 'More Brilliant')", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "allow_in_bindings": 1, "tabs": "all", "toolbar": "full", "media_upload": 0, "delay": 0}, {"key": "field_more_brilliant_subtitle", "label": "Subtitle", "name": "subtitle", "aria-label": "", "type": "text", "instructions": "Subtitle text", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "The 5-step system to doing less while achieving more", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_more_brilliant_coming_soon", "label": "Coming Soon Text", "name": "coming_soon_text", "aria-label": "", "type": "text", "instructions": "Coming soon text", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "COMING SOON", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_course_promise_title", "label": "Course Promise Title", "name": "course_promise_title", "aria-label": "", "type": "text", "instructions": "Title for the course promise card", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Course Promise:", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_course_promise_items", "label": "Course Promise Items", "name": "course_promise_items", "aria-label": "", "type": "repeater", "instructions": "Add course promise bullet points", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 10, "layout": "table", "button_label": "Add Item", "rows_per_page": 20, "collapsed": "", "sub_fields": [{"key": "field_course_promise_item_text", "label": "Item Text", "name": "item_text", "aria-label": "", "type": "text", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_course_promise_items"}]}, {"key": "field_pre_launch_title", "label": "Pre-Launch Benefits Title", "name": "pre_launch_title", "aria-label": "", "type": "text", "instructions": "Title for the pre-launch benefits card", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Pre-Launch Benefits:", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_pre_launch_items", "label": "Pre-Launch Benefits Items", "name": "pre_launch_items", "aria-label": "", "type": "repeater", "instructions": "Add pre-launch benefit bullet points", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 10, "layout": "table", "button_label": "Add Item", "rows_per_page": 20, "collapsed": "", "sub_fields": [{"key": "field_pre_launch_item_text", "label": "Item Text", "name": "item_text", "aria-label": "", "type": "text", "instructions": "", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_pre_launch_items"}]}, {"key": "field_full_course_value", "label": "Full Course Value", "name": "full_course_value", "aria-label": "", "type": "text", "instructions": "Full course value (e.g., '£497')", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_early_bird_price", "label": "<PERSON> <PERSON>", "name": "early_bird_price", "aria-label": "", "type": "text", "instructions": "Early bird price (e.g., '£247')", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_testimonial_quote", "label": "Testimonial Quote", "name": "testimonial_quote", "aria-label": "", "type": "textarea", "instructions": "Testimonial quote text", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "rows": 3, "default_value": "", "new_lines": "", "maxlength": "", "placeholder": ""}, {"key": "field_testimonial_name", "label": "Testimonial Name", "name": "testimonial_name", "aria-label": "", "type": "text", "instructions": "Name of the person giving testimonial", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_testimonial_role", "label": "Testimonial Role", "name": "testimonial_role", "aria-label": "", "type": "text", "instructions": "Role/title of the person giving testimonial", "required": false, "conditional_logic": false, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}], "location": [[{"param": "block", "operator": "==", "value": "acf/gift-more-brilliant"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "Fields for the More Brilliant Block", "show_in_rest": 0, "modified": 1756412054}